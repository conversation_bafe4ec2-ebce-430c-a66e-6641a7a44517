import { Raffle, Participant, RaffleExecutionResult, CreateRaffleData } from '../types';
import { raffleService, participantService } from '../lib/database';
import { bitcoinApiService, BitcoinApiError } from './bitcoinApi';

export class RaffleServiceError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'RaffleServiceError';
  }
}

export const raffleExecutionService = {
  /**
   * Creates a new raffle with participants
   */
  async createRaffle(data: CreateRaffleData): Promise<number> {
    try {
      // Validate input
      if (!data.name.trim()) {
        throw new RaffleServiceError('Raffle name is required');
      }
      
      if (data.participants.length === 0) {
        throw new RaffleServiceError('At least one participant is required');
      }
      
      if (data.scheduledDateTime <= new Date()) {
        throw new RaffleServiceError('Scheduled date and time must be in the future');
      }
      
      // Filter out empty participant names
      const validParticipants = data.participants
        .map(name => name.trim())
        .filter(name => name.length > 0);
      
      if (validParticipants.length === 0) {
        throw new RaffleServiceError('At least one valid participant name is required');
      }
      
      // Create the raffle
      const raffleId = await raffleService.createRaffle({
        name: data.name.trim(),
        description: data.description?.trim() || '',
        scheduledDateTime: data.scheduledDateTime,
        status: 'pending'
      });
      
      // Add participants
      for (const participantName of validParticipants) {
        await participantService.addParticipant({
          name: participantName,
          raffleId
        });
      }
      
      return raffleId;
    } catch (error) {
      console.error('Error creating raffle:', error);
      if (error instanceof RaffleServiceError) {
        throw error;
      }
      throw new RaffleServiceError(`Failed to create raffle: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Executes a raffle by finding the closest Bitcoin block and determining the winner
   */
  async executeRaffle(raffleId: number): Promise<RaffleExecutionResult> {
    try {
      // Get raffle details
      const raffle = await raffleService.getRaffle(raffleId);
      if (!raffle) {
        throw new RaffleServiceError('Raffle not found', 'RAFFLE_NOT_FOUND');
      }
      
      if (raffle.status !== 'pending') {
        throw new RaffleServiceError('Raffle has already been executed or cancelled', 'RAFFLE_NOT_PENDING');
      }
      
      // Check if raffle can be executed (time has passed)
      if (!bitcoinApiService.canExecuteRaffle(raffle.scheduledDateTime)) {
        const timeRemaining = bitcoinApiService.getTimeUntilExecution(raffle.scheduledDateTime);
        throw new RaffleServiceError(
          `Raffle cannot be executed yet. Time remaining: ${timeRemaining}`,
          'RAFFLE_NOT_READY'
        );
      }
      
      // Get participants
      const participants = await participantService.getParticipantsByRaffle(raffleId);
      if (participants.length === 0) {
        throw new RaffleServiceError('No participants found for this raffle', 'NO_PARTICIPANTS');
      }
      
      // Convert scheduled time to Unix timestamp (seconds)
      const targetTimestamp = Math.floor(raffle.scheduledDateTime.getTime() / 1000);
      
      // Get the closest Bitcoin block
      console.log(`Executing raffle ${raffleId} for timestamp ${targetTimestamp}`);
      const bitcoinBlock = await bitcoinApiService.getClosestBlock(targetTimestamp);
      
      // Calculate winner
      const winnerIndex = bitcoinApiService.calculateWinnerIndex(bitcoinBlock.hash, participants.length);
      const winner = participants[winnerIndex];
      
      if (!winner) {
        throw new RaffleServiceError('Failed to determine winner', 'WINNER_CALCULATION_ERROR');
      }
      
      // Get hash decimal for display
      const hashDecimal = bitcoinApiService.hashToDecimal(bitcoinBlock.hash);
      
      // Update raffle with execution results
      await raffleService.executeRaffle(
        raffleId,
        bitcoinBlock,
        winner.id!,
        winner.name,
        hashDecimal,
        winnerIndex
      );
      
      const result: RaffleExecutionResult = {
        winnerId: winner.id!,
        winnerName: winner.name,
        bitcoinBlock,
        hashDecimal,
        winnerIndex
      };
      
      console.log('Raffle executed successfully:', result);
      return result;
      
    } catch (error) {
      console.error('Error executing raffle:', error);
      
      if (error instanceof RaffleServiceError || error instanceof BitcoinApiError) {
        throw error;
      }
      
      throw new RaffleServiceError(
        `Failed to execute raffle: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'EXECUTION_ERROR'
      );
    }
  },

  /**
   * Gets all raffles with their participants
   */
  async getAllRafflesWithParticipants(): Promise<Array<{ raffle: Raffle; participants: Participant[] }>> {
    try {
      const raffles = await raffleService.getAllRaffles();
      const rafflesWithParticipants = await Promise.all(
        raffles.map(async (raffle) => {
          const participants = await participantService.getParticipantsByRaffle(raffle.id!);
          return { raffle, participants };
        })
      );
      
      return rafflesWithParticipants;
    } catch (error) {
      console.error('Error getting raffles with participants:', error);
      throw new RaffleServiceError(
        `Failed to load raffles: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  },

  /**
   * Deletes a raffle and all its participants
   */
  async deleteRaffle(raffleId: number): Promise<void> {
    try {
      const raffle = await raffleService.getRaffle(raffleId);
      if (!raffle) {
        throw new RaffleServiceError('Raffle not found', 'RAFFLE_NOT_FOUND');
      }
      
      await raffleService.deleteRaffle(raffleId);
      console.log(`Raffle ${raffleId} deleted successfully`);
    } catch (error) {
      console.error('Error deleting raffle:', error);
      if (error instanceof RaffleServiceError) {
        throw error;
      }
      throw new RaffleServiceError(
        `Failed to delete raffle: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  },

  /**
   * Validates raffle execution readiness
   */
  validateExecutionReadiness(raffle: Raffle, participants: Participant[]): { canExecute: boolean; reason?: string } {
    if (raffle.status !== 'pending') {
      return { canExecute: false, reason: 'Raffle has already been executed or cancelled' };
    }
    
    if (participants.length === 0) {
      return { canExecute: false, reason: 'No participants found' };
    }
    
    if (!bitcoinApiService.canExecuteRaffle(raffle.scheduledDateTime)) {
      const timeRemaining = bitcoinApiService.getTimeUntilExecution(raffle.scheduledDateTime);
      return { canExecute: false, reason: `Time remaining: ${timeRemaining}` };
    }
    
    return { canExecute: true };
  }
};
