import React from 'react';
import { Calendar, Users, Trophy, Clock, Trash2 } from '@radix-ui/react-icons';
import { Button } from './ui/Button';
import { RaffleCardProps } from '../types';
import { bitcoinApiService } from '../services/bitcoinApi';
import { raffleExecutionService } from '../services/raffleService';

export const RaffleCard: React.FC<RaffleCardProps> = ({
  raffle,
  participants,
  onExecute,
  onDelete
}) => {
  const formatDateTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'executed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const canExecute = raffleExecutionService.validateExecutionReadiness(raffle, participants);
  const timeUntilExecution = bitcoinApiService.getTimeUntilExecution(raffle.scheduledDateTime);

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {raffle.name}
          </h3>
          {raffle.description && (
            <p className="text-gray-600 text-sm mb-2">
              {raffle.description}
            </p>
          )}
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(raffle.status)}`}>
            {raffle.status.charAt(0).toUpperCase() + raffle.status.slice(1)}
          </span>
        </div>
        
        {raffle.status === 'pending' && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(raffle.id!)}
            className="text-red-500 hover:text-red-700 p-2"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>

      <div className="space-y-3 mb-4">
        <div className="flex items-center text-sm text-gray-600">
          <Calendar className="h-4 w-4 mr-2" />
          <span>Scheduled: {formatDateTime(raffle.scheduledDateTime)}</span>
        </div>
        
        <div className="flex items-center text-sm text-gray-600">
          <Users className="h-4 w-4 mr-2" />
          <span>{participants.length} participant{participants.length !== 1 ? 's' : ''}</span>
        </div>

        {raffle.status === 'pending' && (
          <div className="flex items-center text-sm text-gray-600">
            <Clock className="h-4 w-4 mr-2" />
            <span>{timeUntilExecution}</span>
          </div>
        )}

        {raffle.status === 'executed' && raffle.winnerName && (
          <div className="flex items-center text-sm text-green-600">
            <Trophy className="h-4 w-4 mr-2" />
            <span>Winner: {raffle.winnerName}</span>
          </div>
        )}
      </div>

      {participants.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Participants:</h4>
          <div className="flex flex-wrap gap-1">
            {participants.map((participant, index) => (
              <span
                key={participant.id}
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  raffle.status === 'executed' && raffle.winnerIndex === index
                    ? 'bg-green-100 text-green-800 ring-2 ring-green-500'
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                {participant.name}
                {raffle.status === 'executed' && raffle.winnerIndex === index && (
                  <Trophy className="h-3 w-3 ml-1" />
                )}
              </span>
            ))}
          </div>
        </div>
      )}

      {raffle.status === 'executed' && raffle.bitcoinBlock && (
        <div className="bg-gray-50 rounded-md p-3 mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Bitcoin Block Details:</h4>
          <div className="space-y-1 text-xs text-gray-600">
            <div>
              <span className="font-medium">Hash:</span> {raffle.bitcoinBlock.hash.slice(0, 20)}...
            </div>
            <div>
              <span className="font-medium">Height:</span> {raffle.bitcoinBlock.height}
            </div>
            <div>
              <span className="font-medium">Timestamp:</span> {formatDateTime(new Date(raffle.bitcoinBlock.timestamp * 1000))}
            </div>
            {raffle.hashDecimal && (
              <div>
                <span className="font-medium">Hash Decimal:</span> {raffle.hashDecimal}
              </div>
            )}
          </div>
        </div>
      )}

      {raffle.status === 'pending' && (
        <div className="flex justify-end">
          <Button
            onClick={() => onExecute(raffle.id!)}
            disabled={!canExecute.canExecute}
            className="w-full sm:w-auto"
          >
            {canExecute.canExecute ? 'Execute Raffle' : 'Not Ready'}
          </Button>
          {!canExecute.canExecute && canExecute.reason && (
            <p className="text-xs text-gray-500 mt-1 text-center">
              {canExecute.reason}
            </p>
          )}
        </div>
      )}

      {raffle.status === 'executed' && raffle.executedAt && (
        <div className="text-xs text-gray-500 text-center">
          Executed on {formatDateTime(raffle.executedAt)}
        </div>
      )}
    </div>
  );
};
