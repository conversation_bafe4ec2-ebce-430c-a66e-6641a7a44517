import { BitcoinBlock, BitcoinApiResponse } from '../types';

const BITCOIN_API_BASE = 'https://blockchain.info';

export class BitcoinApiError extends Error {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'BitcoinApiError';
  }
}

export const bitcoinApiService = {
  /**
   * Fetches Bitcoin blocks by timestamp
   * @param timestamp Unix timestamp in seconds
   * @returns Promise<BitcoinBlock[]>
   */
  async getBlocksByTimestamp(timestamp: number): Promise<BitcoinBlock[]> {
    try {
      // Convert to milliseconds for the API call
      const timestampMs = timestamp * 1000;
      const url = `${BITCOIN_API_BASE}/blocks/${timestampMs}?format=json`;
      
      console.log(`Fetching Bitcoin blocks for timestamp: ${timestamp} (${new Date(timestampMs).toISOString()})`);
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new BitcoinApiError(
          `Failed to fetch Bitcoin blocks: ${response.status} ${response.statusText}`,
          response.status
        );
      }
      
      const data: BitcoinApiResponse = await response.json();
      
      if (!data.blocks || !Array.isArray(data.blocks)) {
        throw new BitcoinApiError('Invalid response format from Bitcoin API');
      }
      
      return data.blocks;
    } catch (error) {
      if (error instanceof BitcoinApiError) {
        throw error;
      }
      
      console.error('Error fetching Bitcoin blocks:', error);
      throw new BitcoinApiError(
        `Network error while fetching Bitcoin blocks: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  },

  /**
   * Finds the Bitcoin block closest to a given timestamp
   * @param targetTimestamp Unix timestamp in seconds
   * @returns Promise<BitcoinBlock>
   */
  async getClosestBlock(targetTimestamp: number): Promise<BitcoinBlock> {
    try {
      const blocks = await this.getBlocksByTimestamp(targetTimestamp);
      
      if (blocks.length === 0) {
        throw new BitcoinApiError('No blocks found for the given timestamp');
      }
      
      // Find the block with timestamp closest to the target
      let closestBlock = blocks[0];
      let smallestDiff = Math.abs(closestBlock.timestamp - targetTimestamp);
      
      for (const block of blocks) {
        const diff = Math.abs(block.timestamp - targetTimestamp);
        if (diff < smallestDiff) {
          smallestDiff = diff;
          closestBlock = block;
        }
      }
      
      console.log(`Found closest block: ${closestBlock.hash} at ${new Date(closestBlock.timestamp * 1000).toISOString()}`);
      console.log(`Time difference: ${smallestDiff} seconds`);
      
      return closestBlock;
    } catch (error) {
      console.error('Error finding closest block:', error);
      throw error;
    }
  },

  /**
   * Converts a Bitcoin block hash to decimal
   * @param hash Hexadecimal hash string
   * @returns string representation of decimal number
   */
  hashToDecimal(hash: string): string {
    try {
      // Remove '0x' prefix if present
      const cleanHash = hash.replace(/^0x/, '');
      
      // For very large numbers, we'll take the last 15 characters to avoid precision issues
      // This still provides sufficient randomness for the raffle
      const hashSubstring = cleanHash.slice(-15);
      
      // Convert to decimal
      const decimal = parseInt(hashSubstring, 16);
      
      if (isNaN(decimal)) {
        throw new Error('Invalid hash format');
      }
      
      return decimal.toString();
    } catch (error) {
      console.error('Error converting hash to decimal:', error);
      throw new BitcoinApiError(`Failed to convert hash to decimal: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Calculates the winner index based on hash and participant count
   * @param hash Bitcoin block hash
   * @param participantCount Number of participants
   * @returns Winner index (0-based)
   */
  calculateWinnerIndex(hash: string, participantCount: number): number {
    if (participantCount <= 0) {
      throw new BitcoinApiError('Participant count must be greater than 0');
    }
    
    try {
      const decimal = this.hashToDecimal(hash);
      const decimalNumber = BigInt(decimal);
      const winnerIndex = Number(decimalNumber % BigInt(participantCount));
      
      console.log(`Hash: ${hash}`);
      console.log(`Decimal: ${decimal}`);
      console.log(`Participant count: ${participantCount}`);
      console.log(`Winner index: ${winnerIndex}`);
      
      return winnerIndex;
    } catch (error) {
      console.error('Error calculating winner index:', error);
      throw new BitcoinApiError(`Failed to calculate winner: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Validates if the current time is at or after the scheduled raffle time
   * @param scheduledDateTime Date object of scheduled time
   * @returns boolean
   */
  canExecuteRaffle(scheduledDateTime: Date): boolean {
    const now = new Date();
    return now >= scheduledDateTime;
  },

  /**
   * Gets a formatted time remaining until raffle can be executed
   * @param scheduledDateTime Date object of scheduled time
   * @returns string describing time remaining or "Ready to execute"
   */
  getTimeUntilExecution(scheduledDateTime: Date): string {
    const now = new Date();
    const diff = scheduledDateTime.getTime() - now.getTime();
    
    if (diff <= 0) {
      return 'Ready to execute';
    }
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }
};
