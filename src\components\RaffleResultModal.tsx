import React from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { X, Trophy, Copy } from '@radix-ui/react-icons';
import { Button } from './ui/Button';
import { RaffleResultModalProps } from '../types';

export const RaffleResultModal: React.FC<RaffleResultModalProps> = ({
  isOpen,
  onClose,
  raffle,
  participants
}) => {
  const formatDateTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      // You could add a toast notification here
      console.log('Copied to clipboard:', text);
    });
  };

  if (!raffle.bitcoinBlock || raffle.winnerIndex === undefined) {
    return null;
  }

  const winner = participants[raffle.winnerIndex];

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto z-50">
          <div className="flex items-center justify-between mb-6">
            <Dialog.Title className="text-xl font-semibold flex items-center gap-2">
              <Trophy className="h-6 w-6 text-yellow-500" />
              Raffle Results
            </Dialog.Title>
            <Dialog.Close asChild>
              <Button variant="ghost" size="sm" className="p-2">
                <X className="h-4 w-4" />
              </Button>
            </Dialog.Close>
          </div>

          <div className="space-y-6">
            {/* Winner Announcement */}
            <div className="text-center bg-green-50 rounded-lg p-6">
              <Trophy className="h-12 w-12 text-yellow-500 mx-auto mb-3" />
              <h2 className="text-2xl font-bold text-green-800 mb-2">
                🎉 Congratulations! 🎉
              </h2>
              <p className="text-lg text-green-700">
                <span className="font-semibold">{winner?.name}</span> is the winner!
              </p>
              <p className="text-sm text-green-600 mt-2">
                Selected from {participants.length} participant{participants.length !== 1 ? 's' : ''}
              </p>
            </div>

            {/* Raffle Details */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-3">Raffle Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Name:</span>
                  <p className="text-gray-800">{raffle.name}</p>
                </div>
                {raffle.description && (
                  <div>
                    <span className="font-medium text-gray-600">Description:</span>
                    <p className="text-gray-800">{raffle.description}</p>
                  </div>
                )}
                <div>
                  <span className="font-medium text-gray-600">Scheduled:</span>
                  <p className="text-gray-800">{formatDateTime(raffle.scheduledDateTime)}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Executed:</span>
                  <p className="text-gray-800">{raffle.executedAt ? formatDateTime(raffle.executedAt) : 'N/A'}</p>
                </div>
              </div>
            </div>

            {/* Bitcoin Block Information */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="font-semibold text-blue-800 mb-3">Bitcoin Block Information</h3>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="font-medium text-blue-600">Block Hash:</span>
                  <div className="flex items-center gap-2 mt-1">
                    <code className="bg-white px-2 py-1 rounded text-xs font-mono break-all">
                      {raffle.bitcoinBlock.hash}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(raffle.bitcoinBlock!.hash)}
                      className="p-1"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <span className="font-medium text-blue-600">Block Height:</span>
                    <p className="text-blue-800">{raffle.bitcoinBlock.height.toLocaleString()}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-600">Block Timestamp:</span>
                    <p className="text-blue-800">{formatDateTime(new Date(raffle.bitcoinBlock.timestamp * 1000))}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-600">Transaction Count:</span>
                    <p className="text-blue-800">{raffle.bitcoinBlock.tx_count.toLocaleString()}</p>
                  </div>
                  <div>
                    <span className="font-medium text-blue-600">Block Size:</span>
                    <p className="text-blue-800">{(raffle.bitcoinBlock.size / 1024 / 1024).toFixed(2)} MB</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Winner Calculation */}
            <div className="bg-yellow-50 rounded-lg p-4">
              <h3 className="font-semibold text-yellow-800 mb-3">Winner Calculation</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium text-yellow-600">Hash Decimal:</span>
                  <div className="flex items-center gap-2 mt-1">
                    <code className="bg-white px-2 py-1 rounded text-xs font-mono">
                      {raffle.hashDecimal}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(raffle.hashDecimal!)}
                      className="p-1"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <div>
                  <span className="font-medium text-yellow-600">Calculation:</span>
                  <p className="text-yellow-800 font-mono text-xs">
                    {raffle.hashDecimal} % {participants.length} = {raffle.winnerIndex}
                  </p>
                </div>
                <div>
                  <span className="font-medium text-yellow-600">Winner Index:</span>
                  <p className="text-yellow-800">{raffle.winnerIndex} (0-based indexing)</p>
                </div>
              </div>
            </div>

            {/* All Participants */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-3">All Participants</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {participants.map((participant, index) => (
                  <div
                    key={participant.id}
                    className={`flex items-center justify-between p-2 rounded ${
                      index === raffle.winnerIndex
                        ? 'bg-green-100 border-2 border-green-500'
                        : 'bg-white border border-gray-200'
                    }`}
                  >
                    <span className="font-medium">
                      {index}. {participant.name}
                    </span>
                    {index === raffle.winnerIndex && (
                      <Trophy className="h-4 w-4 text-yellow-500" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="flex justify-end mt-6">
            <Button onClick={onClose}>
              Close
            </Button>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
