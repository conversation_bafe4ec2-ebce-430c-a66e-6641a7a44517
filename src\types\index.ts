export type RaffleStatus = 'pending' | 'executed' | 'cancelled';

export interface Participant {
  id?: number;
  name: string;
  raffleId: number;
}

export interface BitcoinBlock {
  hash: string;
  timestamp: number;
  height: number;
  size: number;
  tx_count: number;
  merkle_root: string;
  prev_block: string;
  nonce: number;
  bits: number;
  difficulty: number;
}

export interface Raffle {
  id?: number;
  name: string;
  description?: string;
  scheduledDateTime: Date;
  status: RaffleStatus;
  createdAt: Date;
  updatedAt: Date;
  
  // Execution results
  winnerId?: number;
  winnerName?: string;
  bitcoinBlock?: BitcoinBlock;
  executedAt?: Date;
  hashDecimal?: string;
  winnerIndex?: number;
}

export interface CreateRaffleData {
  name: string;
  description?: string;
  scheduledDateTime: Date;
  participants: string[];
}

export interface RaffleExecutionResult {
  winnerId: number;
  winnerName: string;
  bitcoinBlock: BitcoinBlock;
  hashDecimal: string;
  winnerIndex: number;
}

export interface BitcoinApiResponse {
  blocks: BitcoinBlock[];
}

export interface RaffleFormData {
  name: string;
  description: string;
  scheduledDateTime: string;
  participants: string[];
}

export interface RaffleCardProps {
  raffle: Raffle;
  participants: Participant[];
  onExecute: (raffleId: number) => void;
  onDelete: (raffleId: number) => void;
  onEdit?: (raffleId: number) => void;
}

export interface CreateRaffleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateRaffleData) => void;
}

export interface DeleteConfirmationProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  raffleName: string;
}

export interface RaffleResultModalProps {
  isOpen: boolean;
  onClose: () => void;
  raffle: Raffle;
  participants: Participant[];
}

export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

export interface ErrorState {
  hasError: boolean;
  message?: string;
}

export interface AppState {
  raffles: Raffle[];
  loading: LoadingState;
  error: ErrorState;
}
