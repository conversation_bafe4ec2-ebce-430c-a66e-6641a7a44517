import Dexie, { Table } from 'dexie';

export interface Participant {
  id?: number;
  name: string;
  raffleId: number;
}

export interface BitcoinBlock {
  hash: string;
  timestamp: number;
  height: number;
  size: number;
  tx_count: number;
  merkle_root: string;
  prev_block: string;
  nonce: number;
  bits: number;
  difficulty: number;
}

export interface Raffle {
  id?: number;
  name: string;
  description?: string;
  scheduledDateTime: Date;
  status: 'pending' | 'executed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
  
  // Execution results
  winnerId?: number;
  winnerName?: string;
  bitcoinBlock?: BitcoinBlock;
  executedAt?: Date;
  hashDecimal?: string;
  winnerIndex?: number;
}

export class HashDrawDatabase extends Dexie {
  raffles!: Table<Raffle>;
  participants!: Table<Participant>;

  constructor() {
    super('HashDrawDatabase');
    
    this.version(1).stores({
      raffles: '++id, name, scheduledDateTime, status, createdAt',
      participants: '++id, name, raffleId'
    });
  }
}

export const db = new HashDrawDatabase();

// Helper functions for database operations
export const raffleService = {
  async createRaffle(raffle: Omit<Raffle, 'id' | 'createdAt' | 'updatedAt'>): Promise<number> {
    const now = new Date();
    return await db.raffles.add({
      ...raffle,
      createdAt: now,
      updatedAt: now
    });
  },

  async updateRaffle(id: number, updates: Partial<Raffle>): Promise<void> {
    await db.raffles.update(id, {
      ...updates,
      updatedAt: new Date()
    });
  },

  async getRaffle(id: number): Promise<Raffle | undefined> {
    return await db.raffles.get(id);
  },

  async getAllRaffles(): Promise<Raffle[]> {
    return await db.raffles.orderBy('createdAt').reverse().toArray();
  },

  async deleteRaffle(id: number): Promise<void> {
    // Delete all participants first
    await db.participants.where('raffleId').equals(id).delete();
    // Then delete the raffle
    await db.raffles.delete(id);
  },

  async executeRaffle(
    id: number, 
    bitcoinBlock: BitcoinBlock, 
    winnerId: number, 
    winnerName: string,
    hashDecimal: string,
    winnerIndex: number
  ): Promise<void> {
    await db.raffles.update(id, {
      status: 'executed',
      winnerId,
      winnerName,
      bitcoinBlock,
      executedAt: new Date(),
      hashDecimal,
      winnerIndex,
      updatedAt: new Date()
    });
  }
};

export const participantService = {
  async addParticipant(participant: Omit<Participant, 'id'>): Promise<number> {
    return await db.participants.add(participant);
  },

  async updateParticipant(id: number, updates: Partial<Participant>): Promise<void> {
    await db.participants.update(id, updates);
  },

  async deleteParticipant(id: number): Promise<void> {
    await db.participants.delete(id);
  },

  async getParticipantsByRaffle(raffleId: number): Promise<Participant[]> {
    return await db.participants.where('raffleId').equals(raffleId).toArray();
  },

  async getParticipant(id: number): Promise<Participant | undefined> {
    return await db.participants.get(id);
  }
};
