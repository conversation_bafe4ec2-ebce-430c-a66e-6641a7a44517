import React from 'react';
import * as AlertDialog from '@radix-ui/react-alert-dialog';
import { Button } from './ui/Button';
import { DeleteConfirmationProps } from '../types';

export const DeleteConfirmationModal: React.FC<DeleteConfirmationProps> = ({
  isOpen,
  onClose,
  onConfirm,
  raffleName
}) => {
  return (
    <AlertDialog.Root open={isOpen} onOpenChange={onClose}>
      <AlertDialog.Portal>
        <AlertDialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
        <AlertDialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg p-6 w-full max-w-md z-50">
          <AlertDialog.Title className="text-lg font-semibold mb-2">
            Delete Raffle
          </AlertDialog.Title>
          
          <AlertDialog.Description className="text-gray-600 mb-6">
            Are you sure you want to delete the raffle "{raffleName}"? This action cannot be undone and will also remove all participants.
          </AlertDialog.Description>
          
          <div className="flex justify-end gap-2">
            <AlertDialog.Cancel asChild>
              <Button variant="outline">
                Cancel
              </Button>
            </AlertDialog.Cancel>
            
            <AlertDialog.Action asChild>
              <Button variant="destructive" onClick={onConfirm}>
                Delete
              </Button>
            </AlertDialog.Action>
          </div>
        </AlertDialog.Content>
      </AlertDialog.Portal>
    </AlertDialog.Root>
  );
};
